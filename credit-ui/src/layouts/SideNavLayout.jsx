import {
  faArrowLeft,
  faChartGantt,
  faChartSimple,
  faDashboard,
  faGreaterThan,
  faLessThan,
  faPerson,
  faUser,
  faUsers,
  faUsersRays,
  faBars,
  faTimes,
} from "@fortawesome/free-solid-svg-icons";
import { BsFillBellFill, BsPeople, BsPerson } from "react-icons/bs";
import { TbAnalyze, TbDashboard, TbSettingsFilled } from "react-icons/tb";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React, { useEffect, useState } from "react";
import NavItem from "../components/NavItem";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { setCurrentSideNavSection, toggleNavbar } from "../store/navSlice";
import { BiMoneyWithdraw, BiPackage, BiSearch } from "react-icons/bi";
import { HiViewGrid } from "react-icons/hi";
import AppInput from "../components/AppInput";
import { ThemeSwitch } from "../components/ThemeSwitch";

const sidenavs = [
  {
    text: "Dashboard",
    icon: HiViewGrid,
    path: "/dashboard",
  },
  {
    text: "Applicants",
    icon: BsPeople,
    path: "/applicants",
  },
  {
    text: "Loans",
    icon: BiMoneyWithdraw,
    path: "/loans",
  },
  {
    text: "Products",
    icon: BiPackage,
    path: "/products",
  },
];

const othernavs = [
  {
    text: "Applicant Analysis",
    path: "/analysis",
  },
];

export default function SideNavLayout({ children }) {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const dispatch = useDispatch();
  const currentSideNavSection = useSelector((state) => state.nav.currentSideNavSection);
  const isCollapsed = useSelector((state) => state.nav.collapsed);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggle = () => {
    dispatch(toggleNavbar());
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  useEffect(() => {
    sidenavs.forEach((sidenav) => {
      if (sidenav.path === pathname) {
        dispatch(setCurrentSideNavSection(sidenav.text))
      }
    })
  }, [pathname])

  // Close mobile menu when route changes
  useEffect(() => {
    closeMobileMenu();
  }, [pathname]);

  return (
    <div className="flex h-screen w-full bg-background">
      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={closeMobileMenu}
        />
      )}

      {/* Sidebar */}
      <div
        className={`
          flex flex-col bg-sidebar text-sidebar-foreground border-r border-sidebar-border transition-all duration-300 z-50
          ${isCollapsed ? "w-16" : "w-64"}
          lg:relative lg:translate-x-0
          ${isMobileMenuOpen ? "fixed inset-y-0 left-0 translate-x-0" : "fixed inset-y-0 left-0 -translate-x-full lg:translate-x-0"}
        `}
      >
        {/* Header */}
        <div
          onClick={toggle}
          className="flex items-center gap-3 p-4 border-b border-sidebar-border cursor-pointer hover:bg-sidebar-accent/10 transition-colors"
        >
          <div className="flex items-center justify-center w-8 h-8 bg-sidebar-primary rounded-lg">
            <TbAnalyze className="text-xl text-sidebar-primary-foreground" />
          </div>
          {!isCollapsed && (
            <div className="flex flex-col">
              <div className="font-semibold text-sidebar-foreground">Credit Analytics</div>
              <div className="text-xs text-sidebar-foreground/70">Dashboard</div>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex-1 flex flex-col p-2">
          {!isCollapsed && (
            <div className="text-xs font-medium text-sidebar-foreground/70 px-3 py-2 mb-1">
              Main
            </div>
          )}
          <nav className="space-y-1">
            {sidenavs.map((nav, index) => (
              <NavItem
                key={index}
                isCollapsed={isCollapsed}
                onClick={() => {
                  navigate(nav.path);
                  closeMobileMenu();
                }}
                currentSideNavSection={currentSideNavSection}
                {...nav}
              />
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden lg:ml-0">
        {/* Header */}
        <div className="flex items-center justify-between p-4 lg:p-6 border-b border-border bg-card">
          {/* Mobile Menu Button */}
          <button
            onClick={toggleMobileMenu}
            className="lg:hidden p-2 hover:bg-muted rounded-md transition-colors touch-target"
            aria-label="Toggle mobile menu"
          >
            <FontAwesomeIcon
              icon={isMobileMenuOpen ? faTimes : faBars}
              className="text-lg text-foreground"
            />
          </button>

          <div className="flex items-center gap-2 text-sm text-muted-foreground hidden lg:flex">
            {[...sidenavs].find((nav) => nav.path === pathname) && (
              <>
                <span>Pages</span>
                <span>/</span>
                <span className="text-foreground font-medium">
                  {[...sidenavs].find((nav) => nav.path === pathname)?.text}
                </span>
              </>
            )}
            {[...othernavs].find((nav) => nav.path === pathname) && (
              <>
                <button
                  onClick={() => navigate(-1)}
                  className="p-1 hover:bg-muted rounded-md transition-colors"
                >
                  <FontAwesomeIcon icon={faArrowLeft} className="text-sm" />
                </button>
                <span>Pages</span>
                <span>/</span>
                <span>{currentSideNavSection}</span>
                <span>/</span>
                <span className="text-foreground font-medium">
                  {[...othernavs].find((nav) => nav.path === pathname)?.text}
                </span>
              </>
            )}
          </div>

          <div className="flex items-center gap-2 lg:gap-4">
            {/* Search - Hidden on mobile */}
            <div className="hidden md:block">
              <AppInput icon={BiSearch} placeholder="Type here..." />
            </div>

            {/* User info - Simplified on mobile */}
            <div className="flex items-center gap-2 text-sm font-medium text-foreground">
              <BsPerson className="text-lg" />
              <span className="hidden sm:inline">Ebo</span>
            </div>

            {/* Action buttons */}
            <div className="flex items-center gap-2 lg:gap-3 text-lg text-muted-foreground">
              <ThemeSwitch />
              <button className="p-1 hover:bg-muted rounded-md transition-colors touch-target">
                <TbSettingsFilled />
              </button>
              <button className="p-1 hover:bg-muted rounded-md transition-colors touch-target">
                <BsFillBellFill />
              </button>
            </div>
          </div>
        </div>

        {/* Page Title */}
        <div className="p-4 lg:p-6 pb-2 lg:pb-4">
          <h1 className="text-xl lg:text-2xl font-bold text-foreground">
            {[...sidenavs, ...othernavs].find((nav) => nav.path === pathname)?.text}
          </h1>
        </div>

        {/* Page Content */}
        <div className="flex-1 overflow-y-auto responsive-container pt-0 bg-background">
          {children}
        </div>
      </div>
    </div>
  );
}
