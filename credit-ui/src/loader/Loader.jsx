import React from 'react';
import CustomLoader, { AnalysisLoader } from '../components/CustomLoader';

export default function Loader({ width, height, animationName}) {
    // If it's an analyzing animation, use the specialized AnalysisLoader
    if (animationName === 'analyzing') {
        return <AnalysisLoader />;
    }

    // Default to custom spinner loader
    return (
        <CustomLoader
            width={width || 400}
            height={height || 400}
            variant="spinner"
            text=""
            size="large"
        />
    );
}