import React, { useState, useMemo } from 'react';
import { AreaChart, Area, BarChart, Bar, XAxis, YAxi<PERSON>, Tooltip, ResponsiveContainer } from 'recharts';
import { useTheme } from '../../contexts/ThemeContext';
import ChartTypeSelector from '../selectors/ChartTypeSelector';
import TopModeSelector from '../selectors/TopModeSelector';
import GranularitySelector from '../selectors/GranularitySelector';
import numeral from 'numeral';

const CreditTrends = ({ data = [] }) => {
  const { theme } = useTheme();
  const [chartType, setChartType] = useState('area');
  const [trendMode, setTrendMode] = useState('amount');
  const [granularity, setGranularity] = useState('monthly');

  // Get theme-appropriate colors
  const getChartColor = () => {
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    
    switch (theme) {
      case 'dark':
        return computedStyle.getPropertyValue('--chart-1').trim();
      case 'transflow-light':
        return computedStyle.getPropertyValue('--chart-1').trim();
      case 'transflow-dark':
        return computedStyle.getPropertyValue('--chart-1').trim();
      default:
        return computedStyle.getPropertyValue('--chart-1').trim();
    }
  };

  const chartColor = getChartColor();

  // Process data based on granularity and mode
  const processedData = useMemo(() => {
    if (!data || data.length === 0) {
      // Generate sample data for demonstration
      const sampleData = [];
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      
      for (let i = 0; i < 12; i++) {
        sampleData.push({
          name: months[i],
          amount: Math.floor(Math.random() * 1000000) + 500000,
          count: Math.floor(Math.random() * 100) + 50,
        });
      }
      return sampleData;
    }

    // Process real data based on granularity
    return data.map(item => ({
      name: item.period || item.name || item.date,
      amount: item.credit_amount || item.loan_amount || item.amount || 0,
      count: item.credit_count || item.loan_count || item.count || 0,
    }));
  }, [data, granularity]);

  // Tooltip content
  const tooltipContent = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const value = payload[0].value;
      return (
        <div className="rounded-lg border bg-background p-2 shadow-sm">
          <div className="grid grid-cols-2 gap-2">
            <div className="flex flex-col">
              <span className="text-[0.70rem] uppercase text-muted-foreground">
                Period
              </span>
              <span className="font-bold text-muted-foreground">
                {label}
              </span>
            </div>
            <div className="flex flex-col">
              <span className="text-[0.70rem] uppercase text-muted-foreground">
                {trendMode === 'amount' ? 'Amount' : 'Count'}
              </span>
              <span className="font-bold">
                {trendMode === 'amount' 
                  ? `₵${value?.toLocaleString()}` 
                  : value?.toLocaleString()
                }
              </span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const chartData = processedData.map(item => ({
    name: item.name,
    total: trendMode === 'amount' ? item.amount : item.count
  }));

  return (
    <div className="space-y-4">
      {/* Header with Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-foreground">Credit Trend Over Time</h3>
          <p className="text-sm text-muted-foreground">
            {granularity.charAt(0).toUpperCase() + granularity.slice(1)} credit {trendMode === 'amount' ? 'value' : 'volume'} analysis
          </p>
        </div>
        <div className="flex items-center gap-2 flex-shrink-0">
          <ChartTypeSelector
            value={chartType}
            onValueChange={setChartType}
            label="Type"
            className="min-w-[100px]"
          />
          <TopModeSelector
            mode={trendMode}
            onModeChange={setTrendMode}
            label="Show"
            className="min-w-[120px]"
          />
          <GranularitySelector
            value={granularity}
            onValueChange={setGranularity}
            label="Granularity"
            className="min-w-[120px]"
          />
        </div>
      </div>

      {/* Chart */}
      <div className="h-[350px]">
        <ResponsiveContainer width="100%" height="100%">
          {chartType === 'area' ? (
            <AreaChart data={chartData}>
              <defs>
                <linearGradient id="fillTotal" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={chartColor} stopOpacity={0.8} />
                  <stop offset="95%" stopColor={chartColor} stopOpacity={0.1} />
                </linearGradient>
              </defs>
              <XAxis
                dataKey="name"
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => 
                  trendMode === 'amount' 
                    ? `₵${numeral(value).format('0.0a')}` 
                    : numeral(value).format('0a')
                }
              />
              <Tooltip content={tooltipContent} />
              <Area
                type="monotone"
                dataKey="total"
                stroke={chartColor}
                fillOpacity={1}
                fill="url(#fillTotal)"
              />
            </AreaChart>
          ) : (
            <BarChart data={chartData}>
              <XAxis
                dataKey="name"
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => 
                  trendMode === 'amount' 
                    ? `₵${numeral(value).format('0.0a')}` 
                    : numeral(value).format('0a')
                }
              />
              <Tooltip content={tooltipContent} />
              <Bar
                dataKey="total"
                fill={chartColor}
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          )}
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default CreditTrends;
