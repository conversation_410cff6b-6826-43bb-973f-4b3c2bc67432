import React from "react";

export default function NavItem({ icon: Icon, text, isCollapsed, path, currentSideNavSection, onClick }) {
  const isActive = currentSideNavSection === text;

  return (
    <button
      onClick={onClick}
      className={`
        flex items-center gap-3 w-full px-3 py-3 lg:py-2 text-sm font-medium rounded-lg transition-all duration-200 touch-target
        ${isCollapsed ? 'justify-center' : 'justify-start'}
        ${isActive
          ? 'bg-sidebar-primary text-sidebar-primary-foreground shadow-sm'
          : 'text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'
        }
      `}
    >
      <Icon className="text-lg shrink-0" />
      {!isCollapsed && (
        <span className="truncate">{text}</span>
      )}
    </button>
  );
}
