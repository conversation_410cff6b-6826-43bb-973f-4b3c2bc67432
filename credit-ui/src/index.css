@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.208 0.042 265.755);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.968 0.007 247.896);
  --accent-foreground: oklch(0.208 0.042 265.755);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.208 0.042 265.755);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);

  /* Sidebar specific colors */
  --sidebar: oklch(0.968 0.007 247.896);
  --sidebar-foreground: oklch(0.208 0.042 265.755);
  --sidebar-primary: oklch(0.208 0.042 265.755);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.929 0.013 255.508);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.704 0.04 256.788);
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --card: oklch(0.14 0.04 259.21);
  --card-foreground: oklch(0.984 0.003 247.858);
  --popover: oklch(0.208 0.042 265.755);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --primary: oklch(0.7 0.15 220); /* Brighter blue for better chart visibility */
  --primary-foreground: oklch(0.129 0.042 264.695);
  --secondary: oklch(0.279 0.041 260.031);
  --secondary-foreground: oklch(0.984 0.003 247.858);
  --muted: oklch(0.279 0.041 260.031);
  --muted-foreground: oklch(0.704 0.04 256.788);
  --accent: oklch(0.279 0.041 260.031);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.7 0.15 220); /* Bright blue for charts */
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
}

.transflow-light {
  --background: oklch(0.98 0.01 240); /* Very light blue-tinted background */
  --foreground: oklch(0.15 0.08 240); /* Dark blue text */
  --card: oklch(1 0 0); /* Pure white cards for contrast */
  --card-foreground: oklch(0.15 0.08 240);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.08 240);
  --primary: oklch(0.35 0.15 240); /* #08518A - main transflow blue */
  --primary-foreground: oklch(0.98 0.01 240);
  --secondary: oklch(0.92 0.02 240); /* Light blue secondary */
  --secondary-foreground: oklch(0.35 0.15 240);
  --muted: oklch(0.95 0.01 240); /* Very light blue muted */
  --muted-foreground: oklch(0.4 0.1 240); /* Darker blue for better contrast */
  --accent: oklch(0.35 0.15 240); /* Use primary color for sidebar */
  --accent-foreground: oklch(0.98 0.01 240); /* Light text on blue sidebar */
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.88 0.02 240); /* Light blue borders */
  --input: oklch(0.95 0.01 240); /* Light blue inputs */
  --ring: oklch(0.35 0.15 240);
  --chart-1: oklch(0.35 0.15 240); /* Primary blue for main chart */
  --chart-2: oklch(0.55 0.12 180); /* Complementary teal */
  --chart-3: oklch(0.65 0.15 300); /* Purple accent */
  --chart-4: oklch(0.45 0.18 60); /* Orange accent */
  --chart-5: oklch(0.6 0.14 120); /* Green accent */

  /* Sidebar specific colors - matching rpay-dashboard transflow-light exactly */
  --sidebar: oklch(0.35 0.15 240); /* Blue sidebar background */
  --sidebar-foreground: oklch(0.98 0.01 240); /* Light text on blue */
  --sidebar-primary: oklch(0.25 0.18 240); /* Darker blue for active items */
  --sidebar-primary-foreground: oklch(0.98 0.01 240); /* Light text */
  --sidebar-accent: oklch(0.4 0.12 240); /* Hover state blue */
  --sidebar-accent-foreground: oklch(0.98 0.01 240); /* Light text on hover */
  --sidebar-border: oklch(0.3 0.12 240); /* Darker blue borders */
  --sidebar-ring: oklch(0.25 0.18 240); /* Focus ring */
}

.transflow-dark {
  --background: oklch(0.08 0.02 240); /* Very dark blue background */
  --foreground: oklch(0.92 0.02 240); /* Light blue-tinted text */
  --card: oklch(0.12 0.03 240); /* Dark blue cards */
  --card-foreground: oklch(0.92 0.02 240);
  --popover: oklch(0.15 0.04 240); /* Slightly lighter dark blue for popovers */
  --popover-foreground: oklch(0.92 0.02 240);
  --primary: oklch(0.65 0.18 240); /* Bright blue for visibility in dark mode */
  --primary-foreground: oklch(0.08 0.02 240); /* Dark text on bright blue */
  --secondary: oklch(0.2 0.04 240); /* Dark blue secondary */
  --secondary-foreground: oklch(0.85 0.03 240);
  --muted: oklch(0.18 0.03 240); /* Dark blue muted */
  --muted-foreground: oklch(0.6 0.06 240); /* Medium blue for muted text */
  --accent: oklch(0.25 0.12 240); /* Dark blue for sidebar */
  --accent-foreground: oklch(0.85 0.03 240); /* Light text on dark blue sidebar */
  --destructive: oklch(0.65 0.2 15); /* Bright red for visibility */
  --border: oklch(0.25 0.04 240); /* Dark blue borders */
  --input: oklch(0.15 0.03 240); /* Dark blue inputs */
  --ring: oklch(0.65 0.18 240);
  --chart-1: oklch(0.65 0.18 240); /* Bright blue for main chart - good contrast */
  --chart-2: oklch(0.7 0.15 180); /* Bright teal */
  --chart-3: oklch(0.75 0.18 300); /* Bright purple */
  --chart-4: oklch(0.7 0.2 60); /* Bright orange */
  --chart-5: oklch(0.7 0.16 120); /* Bright green */

  /* Sidebar specific colors */
  --sidebar: var(--accent); /* Dark blue sidebar */
  --sidebar-foreground: var(--accent-foreground); /* Light text on dark blue */
  --sidebar-primary: oklch(0.65 0.18 240); /* Bright blue for primary elements */
  --sidebar-primary-foreground: oklch(0.08 0.02 240);
  --sidebar-accent: oklch(0.35 0.15 240); /* Medium blue for hover states */
  --sidebar-accent-foreground: oklch(0.92 0.02 240);
  --sidebar-border: oklch(0.2 0.06 240); /* Subtle borders in sidebar */
  --sidebar-ring: oklch(0.65 0.18 240);
}

@theme inline {
  --font-inter: 'Inter', 'sans-serif';
  --font-manrope: 'Manrope', 'sans-serif';

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
    scrollbar-width: thin;
    scrollbar-color: var(--border) transparent;
  }
  html {
    @apply overflow-x-hidden;
  }
  body {
    @apply bg-background text-foreground min-h-svh w-full;
  }

  button:not(:disabled),
  [role='button']:not(:disabled) {
    cursor: pointer;
  }

  /* Prevent focus zoom on mobile devices */
  @media screen and (max-width: 767px) {
    input,
    select,
    textarea {
      font-size: 16px !important;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
}

/* Responsive container with proper padding */
.responsive-container {
  margin-inline: auto;
  padding-inline: 1rem;
  max-width: 100%;
}

@media (min-width: 640px) {
  .responsive-container {
    padding-inline: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-container {
    padding-inline: 2rem;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    padding-inline: 2.5rem;
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    padding-inline: 3rem;
  }
}

/* Responsive grid utilities */
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-grid {
    gap: 1.25rem;
  }
}

@media (min-width: 768px) {
  .responsive-grid {
    gap: 1.5rem;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    gap: 1.75rem;
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    gap: 2rem;
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Dashboard specific responsive grid */
.dashboard-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .dashboard-grid {
    gap: 1.25rem;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .dashboard-grid {
    gap: 1.5rem;
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1280px) {
  .dashboard-grid {
    gap: 1.75rem;
    grid-template-columns: repeat(8, 1fr);
  }
}

/* Touch-friendly interactive elements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

@media (max-width: 767px) {
  .touch-target {
    min-height: 48px;
    min-width: 48px;
  }
}

/* Responsive text sizing */
.responsive-text-xs {
  font-size: 0.75rem;
  line-height: 1.125rem;
}

.responsive-text-sm {
  font-size: 0.875rem;
  line-height: 1.375rem;
}

.responsive-text-base {
  font-size: 1rem;
  line-height: 1.625rem;
}

.responsive-text-lg {
  font-size: 1.125rem;
  line-height: 1.875rem;
}

.responsive-text-xl {
  font-size: 1.25rem;
  line-height: 1.875rem;
}

.responsive-text-2xl {
  font-size: 1.375rem;
  line-height: 2rem;
}

@media (min-width: 768px) {
  .responsive-text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .responsive-text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .responsive-text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .responsive-text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .responsive-text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .responsive-text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

/* Responsive spacing utilities */
.responsive-p-sm {
  padding: 0.75rem;
}

.responsive-p-md {
  padding: 1rem;
}

.responsive-p-lg {
  padding: 1.25rem;
}

@media (min-width: 768px) {
  .responsive-p-sm {
    padding: 1rem;
  }

  .responsive-p-md {
    padding: 1.5rem;
  }

  .responsive-p-lg {
    padding: 2rem;
  }
}

/* Responsive margin utilities */
.responsive-m-sm {
  margin: 0.75rem;
}

.responsive-m-md {
  margin: 1rem;
}

.responsive-m-lg {
  margin: 1.25rem;
}

@media (min-width: 768px) {
  .responsive-m-sm {
    margin: 1rem;
  }

  .responsive-m-md {
    margin: 1.5rem;
  }

  .responsive-m-lg {
    margin: 2rem;
  }
}

/* Responsive gap utilities */
.responsive-gap-sm {
  gap: 0.75rem;
}

.responsive-gap-md {
  gap: 1rem;
}

.responsive-gap-lg {
  gap: 1.25rem;
}

@media (min-width: 768px) {
  .responsive-gap-sm {
    gap: 1rem;
  }

  .responsive-gap-md {
    gap: 1.5rem;
  }

  .responsive-gap-lg {
    gap: 2rem;
  }
}

/* Hide/show utilities for responsive design */
.mobile-only {
  display: block;
}

.tablet-up {
  display: none;
}

.desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }

  .tablet-up {
    display: block;
  }
}

@media (min-width: 1024px) {
  .desktop-only {
    display: block;
  }
}

/* Responsive flex utilities */
.responsive-flex-col {
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .responsive-flex-col {
    flex-direction: row;
  }
}

.responsive-flex-row {
  display: flex;
  flex-direction: row;
}

@media (max-width: 767px) {
  .responsive-flex-row {
    flex-direction: column;
  }
}

@utility no-scrollbar {
  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* styles.css */
.CollapsibleContent {
  overflow: hidden;
}
.CollapsibleContent[data-state='open'] {
  animation: slideDown 300ms ease-out;
}
.CollapsibleContent[data-state='closed'] {
  animation: slideUp 300ms ease-out;
}

@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-collapsible-content-height);
  }
  to {
    height: 0;
  }
}